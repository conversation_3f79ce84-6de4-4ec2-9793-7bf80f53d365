// Content utility for parsing home page markdown content

export interface HomeSection {
  id: number
  title: string
  content: Record<string, unknown>
}

export interface HeroContent {
  headline: string
  subheadline: string
  ctaButton: string
  additionalText?: string
}

export interface ProductOverviewContent {
  title: string
  features: Array<{
    title: string
    description: string
  }>
}

export interface ProblemSolutionContent {
  title: string
  problem: string
  solution: string
  benefits: string
}

export interface HowItWorksContent {
  title: string
  steps: Array<{
    number: number
    title: string
    image: string
    text: string
  }>
}

export interface ThreePillarsContent {
  title: string
  layout: string
  pillars: Array<{
    title: string
    mockup: string
    description: string
  }>
}

export interface ScienceContent {
  title: string
  statistic: string
  citations: string
  socialCredibility: string
  visual: string
}

export interface ProductFeaturesContent {
  title: string
  layout: string
  features: string
}

export interface WaitlistCTAContent {
  background: string
  headline: string
  subheadline: string
  emailInput: string
  button: string
  text: string
}

// Parse the markdown content into structured data
export function parseHomeContent(): {
  hero: HeroContent
  productOverview: ProductOverviewContent
  problemSolution: ProblemSolutionContent
  howItWorks: HowItWorksContent
  threePillars: ThreePillarsContent
  science: ScienceContent
  productFeatures: ProductFeaturesContent
  waitlistCTA: WaitlistCTAContent
} {
  // For now, return structured content based on the markdown
  // In a real implementation, you could parse the actual markdown file
  return {
    hero: {
      headline: "Revitalizing Human Energy. One cell at a time.",
      subheadline: "Just 30 minutes a day, 3 times a week. Scientifically proven to increase VO2 max and extend healthspan.",
      ctaButton: "JOIN THE WAITLIST",
      additionalText: "What happens to the cells as we age?"
    },
    productOverview: {
      title: "Vitaliti Air at a glance",
      features: [
        {
          title: "30 min sessions",
          description: "Simple time commitment"
        },
        {
          title: "3x per week", 
          description: "Minimal lifestyle disruption"
        },
        {
          title: "Proven results",
          description: "Increased VO2 max & longevity"
        }
      ]
    },
    problemSolution: {
      title: "The Age Old Problem",
      problem: "As we age, cellular degeneration becomes the leading cause of three kinds of diseases - Metabolic, Cardiovascular, and Neurodegenerative",
      solution: "Recent findings show a compelling link between increasing VO2 max by revitalizing mitochondria. Once the mitochondrial cells are revitalzied, the benefits cascade throughout the body, improving cardiovascular, metabolic, and cognitive health.",
      benefits: "Vitaliti Air is the worlld's first in-home device that helps regenerate mitochondria, leading to increased VO2 max and extended healthy lifespan."
    },
    howItWorks: {
      title: "Transform Your Health in 3 Simple Steps",
      steps: [
        {
          number: 1,
          title: "Wear the Mask",
          image: "Product shot of the Vitaliti Air mask",
          text: "Put on your Vitaliti Air mask for just 30 minutes"
        },
        {
          number: 2,
          title: "Breathe Naturally",
          image: "Person relaxing while wearing mask, reading or working",
          text: "Continue your normal activities while the mask optimizes your oxygen intake"
        },
        {
          number: 3,
          title: "Track Your Progress",
          image: "App mockup showing VO2 max improvements",
          text: "Monitor your health improvements through our companion app"
        }
      ]
    },
    threePillars: {
      title: "One Device. Three Life-Changing Benefits.",
      layout: "Alternating left/right layouts with device mockups",
      pillars: [
        {
          title: "Cardiovascular Health",
          mockup: "Mockup showing heart rate and VO2 max metrics",
          description: "Strengthen your heart and improve oxygen delivery throughout your body"
        },
        {
          title: "Metabolic Health & Diabetes Prevention",
          mockup: "Mockup showing glucose levels and metabolic markers",
          description: "Optimize your metabolism and reduce diabetes risk"
        },
        {
          title: "Cognitive Performance",
          mockup: "Mockup showing focus and mental clarity metrics",
          description: "Enhance brain function and protect against cognitive decline"
        }
      ]
    },
    science: {
      title: "Backed by Research",
      statistic: "Every 1 mL/kg/min increase in VO2 max = 9% reduction in mortality risk",
      citations: "Research citations displayed elegantly",
      socialCredibility: "Social credibility",
      visual: "Pictures of the three medical experts and links to studies (TBD)"
    },
    productFeatures: {
      title: "Designed for Real Life",
      layout: "Grid layout showcasing features and the App integration",
      features: "Features TBD"
    },
    waitlistCTA: {
      background: "Full-width gradient section",
      headline: "Be Among the First to Experience Vitaliti Air",
      subheadline: "Limited early access available. Join our waitlist today.",
      emailInput: "Email input field",
      button: "JOIN WAITLIST",
      text: "Get exclusive early-bird pricing and be first to know when we launch"
    }
  }
}

// Get content for a specific section
export function getSectionContent(sectionId: number) {
  const content = parseHomeContent()
  
  switch (sectionId) {
    case 1: return content.hero
    case 2: return content.productOverview
    case 3: return content.problemSolution
    case 4: return content.howItWorks
    case 5: return content.threePillars
    case 6: return content.science
    case 7: return content.productFeatures
    case 8: return content.waitlistCTA
    default: return null
  }
}
